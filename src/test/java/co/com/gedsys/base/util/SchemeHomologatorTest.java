package co.com.gedsys.base.util;

import co.com.gedsys.base.adapter.http.produccion.documentos.*;
import co.com.gedsys.base.domain.metadato.enums.TipoMetadatoEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ValidationException;
import jakarta.validation.Validator;
import jakarta.validation.Validation;
import jakarta.validation.ValidatorFactory;
import jakarta.validation.constraints.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class SchemeHomologatorTest {

    private ObjectMapper objectMapper;
    private Validator validator;

    @BeforeEach
    void setUp() {
        this.objectMapper = new ObjectMapper();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        this.validator = factory.getValidator();
    }

    private record SampleSchema(
            @NotNull String requiredField,
            @Size(min = 3, max = 10) String sizeField,
            @Min(1) int minField,
            @NotEmpty List<@NotBlank String> nonEmptyList
    ) {
    }

    @Test
    public void testValidateAndConvertToSchema_ValidInput() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Some Value");
        input.put("sizeField", "Length");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of("item1", "item2"));

        SampleSchema result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Some Value", result.requiredField());
        assertEquals("Length", result.sizeField());
        assertEquals(5, result.minField());
        assertEquals(List.of("item1", "item2"), result.nonEmptyList());
    }

    @Test
    public void testValidateAndConvertToSchema_MissingRequiredField() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("sizeField", "Length");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of("item1", "item2"));

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("requiredField"));
    }

    @Test
    public void testValidateAndConvertToSchema_InvalidSizeField() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Value");
        input.put("sizeField", "Too Long Value");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of("item1", "item2"));

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("sizeField"));
    }

    @Test
    public void testValidateAndConvertToSchema_InvalidMinField() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Value");
        input.put("sizeField", "Valid");
        input.put("minField", 0);
        input.put("nonEmptyList", List.of("item1", "item2"));

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("minField"));
    }

    @Test
    public void testValidateAndConvertToSchema_EmptyList() {
        SchemeHomologator<SampleSchema> homologator = new SchemeHomologator<>(SampleSchema.class);

        Map<String, Object> input = new HashMap<>();
        input.put("requiredField", "Value");
        input.put("sizeField", "Valid");
        input.put("minField", 5);
        input.put("nonEmptyList", List.of());

        ValidationException exception = assertThrows(ValidationException.class, () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("nonEmptyList"));
    }

    @Test
    public void testSolicitudRecepcionDocumento_ConDatosAnidados() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = crearJsonDePrueba();

        SolicitudRecepcionDocumento result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Informe de Actividades Q3", result.titulo());
        assertEquals("9f1c3de8-b12a-4b0e-9c1e-2fce6e7e5b5b", result.fileId());
        assertEquals("169c4894-637f-4c0c-83a5-7d494841eb73", result.tipoDocumentalId());
        assertEquals("0d405a15-11a2-426a-89ca-7aede3280887", result.unidadDocumentalId());
        assertEquals("jmarin", result.autor());
        assertEquals("c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d", result.remitenteId());
        assertEquals("emontoya", result.destinatarioInterno());

        assertNotNull(result.propiedadesRadicado());
        assertEquals(200, result.propiedadesRadicado().height());
        assertEquals(1, result.propiedadesRadicado().page());
        assertEquals(300, result.propiedadesRadicado().width());
        assertEquals(50, result.propiedadesRadicado().x());
        assertEquals(100, result.propiedadesRadicado().y());
        assertEquals(0, result.propiedadesRadicado().rotationDegrees());

        assertNotNull(result.metadatos());
        assertEquals(1, result.metadatos().size());
        assertEquals("folios", result.metadatos().get(0).nombre());
        assertEquals("1", result.metadatos().get(0).valor());
        assertEquals(TipoMetadatoEnum.CONTENIDO, result.metadatos().get(0).tipo());

        assertNotNull(result.anexos());
        assertEquals(1, result.anexos().size());
        assertEquals("Balance_Q2", result.anexos().get(0).nombre());
        assertEquals("Balance financiero del segundo trimestre", result.anexos().get(0).descripcion());
        assertEquals("a7425c3f-4d8b-4219-842f-eeb9e8dc9ab3", result.anexos().get(0).fileId());
        assertEquals("5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8", result.anexos().get(0).hash());
        assertEquals(204800L, result.anexos().get(0).bytes());
        assertEquals("pdf", result.anexos().get(0).extension());
    }

    @Test
    public void testSolicitudRecepcionDocumento_ConDatosDesordenados() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("destinatarioInterno", "usuario.interno");
        input.put("remitenteId", "remitente-123");
        input.put("anexos", List.of(Map.of(
                "extension", "docx",
                "bytes", 1024L,
                "hash", "hash123",
                "fileId", "file-456",
                "descripcion", "Anexo de prueba",
                "nombre", "Documento"
        )));
        input.put("propiedadesRadicado", Map.of(
                "rotationDegrees", 90,
                "y", 200,
                "x", 150,
                "width", 400,
                "page", 2,
                "height", 300
        ));
        input.put("metadatos", List.of(Map.of(
                "tipo", "CONTENIDO",
                "valor", "5",
                "nombre", "paginas"
        )));
        input.put("autor", "autor.test");
        input.put("unidadDocumentalId", "unidad-789");
        input.put("tipoDocumentalId", "tipo-456");
        input.put("fileId", "archivo-123");
        input.put("titulo", "Documento de Prueba");

        SolicitudRecepcionDocumento result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Documento de Prueba", result.titulo());
        assertEquals("archivo-123", result.fileId());
        assertEquals("tipo-456", result.tipoDocumentalId());
        assertEquals("unidad-789", result.unidadDocumentalId());
        assertEquals("autor.test", result.autor());
        assertEquals("remitente-123", result.remitenteId());
        assertEquals("usuario.interno", result.destinatarioInterno());
    }

    @Test
    public void testSolicitudRecepcionDocumento_CamposRequeridos() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("titulo", "");
        input.put("fileId", "file-123");
        input.put("tipoDocumentalId", "tipo-123");
        input.put("unidadDocumentalId", "unidad-123");
        input.put("autor", "autor");
        input.put("remitenteId", "remitente-123");
        input.put("propiedadesRadicado", Map.of(
                "height", 100, "page", 1, "width", 200, "x", 50, "y", 50, "rotationDegrees", 0
        ));

        ValidationException exception = assertThrows(ValidationException.class,
                () -> homologator.validateAndConvertToSchema(input));
        assertTrue(exception.getMessage().contains("titulo"));
    }

    private Map<String, Object> crearJsonDePrueba() {
        Map<String, Object> variables = new HashMap<>();
        variables.put("autor", "dplata");
        variables.put("titulo", "Informe de Actividades Q3");
        variables.put("fileId", "9f1c3de8-b12a-4b0e-9c1e-2fce6e7e5b5b");
        variables.put("tipoDocumentalId", "169c4894-637f-4c0c-83a5-7d494841eb73");
        variables.put("unidadDocumentalId", "0d405a15-11a2-426a-89ca-7aede3280887");
        variables.put("autor", "jmarin");
        variables.put("metadatos", List.of(Map.of(
                "nombre", "folios",
                "valor", "1",
                "tipo", "CONTENIDO"
        )));
        variables.put("propiedadesRadicado", Map.of(
                "height", 200,
                "page", 1,
                "width", 300,
                "x", 50,
                "y", 100,
                "rotationDegrees", 0
        ));
        variables.put("anexos", List.of(Map.of(
                "nombre", "Balance_Q2",
                "descripcion", "Balance financiero del segundo trimestre",
                "fileId", "a7425c3f-4d8b-4219-842f-eeb9e8dc9ab3",
                "hash", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",
                "bytes", 204800L,
                "extension", "pdf"
        )));
        variables.put("remitenteId", "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d");
        variables.put("destinatarioInterno", "emontoya");

        Map<String, Object> input = new HashMap<>();
        input.put("variables", variables);
        return input;
    }

    @Test
    public void testSolicitudRecepcionDocumento_DirectFields() {
        SchemeHomologator<SolicitudRecepcionDocumento> homologator = new SchemeHomologator<>(SolicitudRecepcionDocumento.class);

        Map<String, Object> input = new HashMap<>();
        input.put("titulo", "Informe de Actividades Q3");
        input.put("fileId", "9f1c3de8-b12a-4b0e-9c1e-2fce6e7e5b5b");
        input.put("tipoDocumentalId", "169c4894-637f-4c0c-83a5-7d494841eb73");
        input.put("unidadDocumentalId", "0d405a15-11a2-426a-89ca-7aede3280887");
        input.put("autor", "jmarin");
        input.put("remitenteId", "c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d");
        input.put("destinatarioInterno", "emontoya");
        input.put("metadatos", List.of(Map.of(
                "nombre", "folios",
                "valor", "1",
                "tipo", "CONTENIDO"
        )));
        input.put("propiedadesRadicado", Map.of(
                "height", 200,
                "page", 1,
                "width", 300,
                "x", 50,
                "y", 100,
                "rotationDegrees", 0
        ));
        input.put("anexos", List.of(Map.of(
                "nombre", "Balance_Q2",
                "descripcion", "Balance financiero del segundo trimestre",
                "fileId", "a7425c3f-4d8b-4219-842f-eeb9e8dc9ab3",
                "hash", "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",
                "bytes", 204800L,
                "extension", "pdf"
        )));

        SolicitudRecepcionDocumento result = homologator.validateAndConvertToSchema(input);

        assertNotNull(result);
        assertEquals("Informe de Actividades Q3", result.titulo());
        assertEquals("jmarin", result.autor());
        assertEquals("c7d6e5f4-a3b2-2a1b-6e5f-4d3c2b1a9e8d", result.remitenteId());
    }
}